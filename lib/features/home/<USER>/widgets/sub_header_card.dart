import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;

/// A reusable card widget for displaying question parts with progress indicators
///
/// Features:
/// - Progress bar showing completion status
/// - Mandatory indicator (red exclamation mark)
/// - Consistent styling following app design patterns
/// - Tap navigation to question part measurement details
/// - Clean, readable layout with proper spacing
class SubHeaderCard extends StatelessWidget {
  final entities.QuestionPart questionPart;
  final entities.Question question;
  final bool isMandatory;
  final num? taskId;
  final num? formId;

  /// Constructor for SubHeaderItemCard

  const SubHeaderCard({
    super.key,
    required this.questionPart,
    required this.question,
    required this.isMandatory,
    this.taskId,
    this.formId,
  });

  /// Calculate progress for this question part based on measurements
  /// Since we don't have access to actual measurement values here,
  /// we'll show a placeholder progress that can be enhanced later
  double _calculateProgress() {
    if (question.measurements == null || question.measurements!.isEmpty) {
      return 0.0;
    }

    // For demonstration purposes, show partial progress
    // In a real implementation, this would check actual measurement values
    // from a state management solution or passed-in completion data
    return 0.0; // Start with 0% progress
  }

  /// Get progress text showing completed vs total measurements
  String _getProgressText() {
    if (question.measurements == null || question.measurements!.isEmpty) {
      return '0 of 0';
    }

    int totalMeasurements = question.measurements!.length;
    // For now, show 0 completed since we don't have access to actual values
    // This would be enhanced to show real completion status
    return '0 of $totalMeasurements';
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final progress = _calculateProgress();
    final progressText = _getProgressText();

    return Stack(
      children: [
        Container(
          margin: const EdgeInsets.symmetric(vertical: 4.0),
        
         
         decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: InkWell(
            borderRadius: BorderRadius.circular(10),
            onTap: () {
              context.router.push(QPMDRoute(
                question: question,
                questionPart: questionPart,
                taskId: taskId,
                formId: formId,
              ));
            },
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title row with mandatory indicator
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          questionPart.questionpartDescription ??
                              'Unnamed Part',
                          style: textTheme.montserratTitleExtraSmall,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (isMandatory) ...[
                        const Gap(8),
                        Container(
                          width: 16,
                          height: 16,
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.priority_high,
                            color: Colors.white,
                            size: 10,
                          ),
                        ),
                      ],
                    ],
                  ),

                  const Gap(16),

                  // Progress bar
                  Row(
                    children: [
                      Expanded(
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(4),
                          child: LinearProgressIndicator(
                            value: progress,
                            backgroundColor: Colors.grey.shade200,
                            valueColor: const AlwaysStoppedAnimation<Color>(
                                AppColors.primaryBlue),
                            minHeight: 6,
                          ),
                        ),
                      ),
                      const Gap(24),
                      Text(
                        progressText,
                        style: textTheme.montserratTableSmall,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
